import { fail } from '@sveltejs/kit';
import { message, superValidate } from 'sveltekit-superforms';
import { zod4 } from 'sveltekit-superforms/adapters';
import { z } from 'zod/v4';

const zodSATScore = z.int().min(400, 'SAT score must be at least 400').max(1600, 'SAT score must be at most 1600').multipleOf(10, 'SAT score must be a multiple of 10');

const schema = z.object({
    name: z.string().min(1, 'Name is required'),
    email: z.email('Invalid email'),
    grade: z.int().min(1, 'Grade is required'),
    test_date: z.date().min(new Date(), 'Test date must be in the future'),
    current_sat: zodSATScore.default(400),
    target_sat: zodSATScore.default(1600)
});

export const load = async () => {
    const form = await superValidate(zod4(schema));
    return { form };
};

export const actions = {
    default: async ({ request }) => {
        const form = await superValidate(request, zod4(schema));

        if (!form.valid) {
            return fail(400, { form });
        }
        
        return message(form, 'Form submitted');
    }
};